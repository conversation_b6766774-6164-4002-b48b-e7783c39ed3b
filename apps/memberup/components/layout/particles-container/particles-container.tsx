import { Particles } from '@/components/magicui/particles'
import { useStore } from '@/hooks/useStore'
import { cn } from '@/lib/utils'

export function ParticlesContainer({ children, className }: { children?: React.ReactNode; className?: string }) {
  const communityColors = useStore((state) => state.community.colors)

  return (
    <div
      className={cn(
        'relative mb-8 max-w-[23.375rem] grow-0 bg-gradient-to-b dark:from-[#121118] dark:to-[#140F1E] sm:mb-0 lg:max-w-full',
        className,
      )}
    >
      <Particles
        className="absolute left-0 top-0 z-0 h-full w-full opacity-60"
        size={1.5}
        color={communityColors.primary}
        staticity={50}
      />
      <div className="relative z-10">{children}</div>
    </div>
  )
}
