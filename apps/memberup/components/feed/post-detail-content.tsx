import React from 'react'

import { useRenderTextWithMentions } from '../../src/components/hooks/use-render-with-mentions'
import { DialogTitle } from '../ui/dialog'
import { IFeed } from '@memberup/shared/src/types/interfaces'
import FeedAttachments from '@/components/feed/feed-attachments'
import PostLinks from '@/components/feed/post-links'
import { cn } from '@/lib/utils'
import { isUUIDv4 } from '@/memberup/libs/utils'
import { selectMembersMap } from '@/memberup/store/features/memberSlice'
import { useAppSelector } from '@/memberup/store/hooks'

export function PostDetailContent({
  className,
  feed,
  isPostPage,
}: {
  className?: string
  feed: IFeed
  isPostPage?: boolean
}) {
  const membersMap = useAppSelector((state) => selectMembersMap(state))

  const renderedText = useRenderTextWithMentions(feed.text, feed?.mentioned_users, membersMap, 'single', feed)

  return (
    <div className={cn('post-detail-content h-auto px-5 md:h-full md:px-5', className)}>
      {feed.title && (
        <div className={cn('flex items-center gap-1 text-black-700 opacity-100 dark:text-white-500')}>
          {!isUUIDv4(feed.title) && (
            <div>
              <h2 className="mb-2.5 text-base font-medium leading-6 text-black-700 dark:text-white-500">
                {isPostPage ? (
                  <>{feed.title}</>
                ) : (
                  <DialogTitle asChild className="mb-0 mt-0 text-base font-medium">
                    <span>{feed.title}</span>
                  </DialogTitle>
                )}
              </h2>
            </div>
          )}
        </div>
      )}
      <div className="text-sbase text-black-600 dark:text-white-200 [&_a:hover]:underline [&_a]:font-bold">
        {renderedText}
        <PostLinks post={feed} />
        <FeedAttachments feed={feed} />
      </div>
    </div>
  )
}
